import 'package:dandanplay_flutter/model/danmaku.dart';

/// 配置项键值枚举
/// 使用枚举确保类型安全，避免字符串硬编码
enum ConfigKey {
  // 弹幕相关配置
  danmakuSettings,

  // 界面相关配置
  themeMode, // 0: system, 1: light, 2: dark
  language,
}

/// 配置项定义类
/// 封装配置项的键、默认值和存储键
class ConfigItem<T> {
  /// 配置项键
  final ConfigKey key;

  /// 默认值
  final T defaultValue;

  /// 在SharedPreferences中的存储键
  final String storageKey;

  /// 配置项描述（可选）
  final String? description;

  const ConfigItem({
    required this.key,
    required this.defaultValue,
    required this.storageKey,
    this.description,
  });
}

/// 配置项注册表
/// 定义所有可用的配置项及其默认值
class ConfigRegistry {
  static final Map<ConfigKey, ConfigItem> _items = {
    // 弹幕相关配置
    ConfigKey.danmakuSettings: ConfigItem<String>(
      key: ConfigKey.danmakuSettings,
      defaultValue: DanmakuSettings.defaultJson(),
      storageKey: 'danmaku_settings',
      description: '弹幕设置',
    ),

    // 界面相关配置
    ConfigKey.themeMode: ConfigItem<int>(
      key: ConfigKey.themeMode,
      defaultValue: 0, // 0: system, 1: light, 2: dark
      storageKey: 'theme_mode',
      description: '主题模式 (0:跟随系统, 1:浅色, 2:深色)',
    ),
    ConfigKey.language: ConfigItem<String>(
      key: ConfigKey.language,
      defaultValue: 'zh_CN',
      storageKey: 'language',
      description: '语言设置',
    ),
  };

  /// 获取配置项定义
  static ConfigItem<T>? getItem<T>(ConfigKey key) {
    final item = _items[key];
    if (item is ConfigItem<T>) {
      return item;
    }
    return null;
  }

  /// 获取所有配置项
  static Map<ConfigKey, ConfigItem> get allItems => Map.unmodifiable(_items);

  /// 获取配置项的默认值
  static T? getDefaultValue<T>(ConfigKey key) {
    final item = getItem<T>(key);
    return item?.defaultValue;
  }

  /// 获取配置项的存储键
  static String? getStorageKey(ConfigKey key) {
    return _items[key]?.storageKey;
  }
}
