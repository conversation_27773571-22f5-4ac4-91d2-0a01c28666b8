import 'dart:io';

import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';

class FileExplorerPage extends StatefulWidget {
  final int mediaLibraryId;
  final String mediaLibraryName;
  const FileExplorerPage({
    super.key,
    required this.mediaLibraryId,
    required this.mediaLibraryName,
  });

  @override
  State<FileExplorerPage> createState() => _FileExplorerPageState();
}

class _FileExplorerPageState extends State<FileExplorerPage> {
  MediaLibrary? _mediaLibrary;
  final FileExplorerService _fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  final ScrollController _scrollController = ScrollController();
  var _refreshFlag = 0;

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    final mediaLibrary = await mediaLibraryService.getMediaLibrary(
      widget.mediaLibraryId,
    );
    if (mediaLibrary == null) {
      return;
    }
    late FileExplorerProvider provider;
    switch (mediaLibrary.mediaLibraryType) {
      case MediaLibraryType.webdav:
        provider = WebDAVFileExplorerProvider(mediaLibrary);
        break;
      case MediaLibraryType.ftp:
        break;
      case MediaLibraryType.smb:
        break;
      case MediaLibraryType.local:
        provider = LocalFileExplorerProvider();
        break;
    }
    _fileExplorerService.setProvider(provider, mediaLibrary);
    setState(() {
      _mediaLibrary = mediaLibrary;
    });
  }

  // TODO 将进度嵌入图片
  Future<Widget> _buildPerfix(History? history) async {
    if (history != null) {
      final directory = await getApplicationDocumentsDirectory();
      return ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Image.file(
          File('${directory.path}/screenshots/${history.uniqueKey}'),
          fit: BoxFit.fitHeight,
          key: ValueKey('${history.uniqueKey}$_refreshFlag'),
          errorBuilder: (context, error, stackTrace) {
            return const Icon(FIcons.play, size: 50);
          },
        ),
      );
    }
    return const Icon(FIcons.play, size: 50);
  }

  void _playVideo(FileItem file, int index) {
    _fileExplorerService.changeVideo(index, file.path);
    final location = Uri(path: videoPlayerPath);
    context.push(location.toString()).then((_) => _refresh());
  }

  Future<void> _refresh() async {
    setState(() {
      _refreshFlag++;
    });
    await _fileExplorerService.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (!_fileExplorerService.goBack()) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          scrolledUnderElevation: 0,
          title: Row(
            children: [
              Text(
                widget.mediaLibraryName,
                style: context.theme.typography.xl2.copyWith(height: 1.2),
              ),
            ],
          ),
        ),
        body:
            _mediaLibrary == null
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                  onRefresh: _refresh,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        controller: _scrollController,
                        child: Watch((context) {
                          final path = _fileExplorerService.path.watch(context);
                          final parts =
                              path
                                  .split('/')
                                  .where((p) => p.isNotEmpty)
                                  .toList();

                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (_scrollController.hasClients &&
                                _scrollController.position.maxScrollExtent >
                                    0) {
                              _scrollController.jumpTo(
                                _scrollController.position.maxScrollExtent,
                              );
                            }
                          });

                          final children = <Widget>[
                            FBreadcrumbItem(
                              onPress: () => _fileExplorerService.cd('/'),
                              child: Text(
                                '根目录',
                                style: TextStyle(
                                  color:
                                      parts.isEmpty
                                          ? context.theme.colors.primary
                                          : context.theme.colors.foreground,
                                ),
                              ),
                            ),
                          ];

                          var currentPath = '';
                          for (var i = 0; i < parts.length; i++) {
                            final part = parts[i];
                            currentPath += '/$part';
                            final targetPath = currentPath;
                            final isLast = i == parts.length - 1;
                            children.add(
                              FBreadcrumbItem(
                                onPress:
                                    isLast
                                        ? null
                                        : () =>
                                            _fileExplorerService.cd(targetPath),
                                child: Text(
                                  part,
                                  style: TextStyle(
                                    color:
                                        isLast
                                            ? context.theme.colors.primary
                                            : context.theme.colors.foreground,
                                  ),
                                ),
                              ),
                            );
                          }

                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: FBreadcrumb(children: children),
                          );
                        }),
                      ),
                      Expanded(
                        child: Watch(
                          (context) => _fileExplorerService.files.value.map(
                            data: (files) {
                              if (files.isEmpty) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        FIcons.folder,
                                        size: 48,
                                        color:
                                            context
                                                .theme
                                                .colors
                                                .mutedForeground,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        '此文件夹为空',
                                        style: context.theme.typography.xl,
                                      ),
                                    ],
                                  ),
                                );
                              }
                              return CustomScrollView(
                                slivers: [
                                  SliverToBoxAdapter(
                                    child: FItemGroup(
                                      divider: FItemDivider.indented,
                                      children: _listBuilder(files),
                                    ),
                                  ),
                                ],
                              );
                            },
                            error:
                                (error, stack) =>
                                    const Center(child: Text('加载失败')),
                            loading:
                                () => const Center(
                                  child: CircularProgressIndicator(),
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  List<FItemMixin> _listBuilder(List<FileItem> files) {
    final widgetList = <FItemMixin>[];
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      if (file.isFolder) {
        widgetList.add(
          FItem(
            prefix: const Icon(FIcons.folder, size: 40),
            title: Text(
              file.name,
              style: context.theme.typography.base,
              maxLines: 2,
            ),
            subtitle: Text('目录'),
            onPress: () => {_fileExplorerService.goDir(file.name)},
          ),
        );
        continue;
      }
      widgetList.add(
        FItem(
          prefix: SizedBox(
            width: 90,
            height: 70,
            child: FutureBuilder(
              future: _buildPerfix(file.history),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }
                if (snapshot.hasData) {
                  return file.isVideo
                      ? snapshot.data!
                      : const Icon(FIcons.file, size: 50);
                }
                return const Icon(FIcons.file, size: 50);
              },
            ),
          ),
          title: ConstrainedBox(
            constraints: const BoxConstraints(minHeight: 70),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  file.name,
                  style: context.theme.typography.base,
                  maxLines: 3,
                ),
                Text(
                  style: context.theme.itemStyle.contentStyle.subtitleTextStyle
                      .resolve({}),
                  file.history != null
                      ? '观看进度: ${formatTime(file.history!.position, file.history!.duration)}'
                      : '未观看',
                ),
              ],
            ),
          ),
          onPress: () => _playVideo(file, i),
        ),
      );
    }
    return widgetList;
  }
}
