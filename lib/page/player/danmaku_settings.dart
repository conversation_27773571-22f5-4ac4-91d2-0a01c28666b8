import 'package:dandanplay_flutter/service/player/danmaku.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:signals_flutter/signals_flutter.dart';
import '../../model/danmaku.dart';

/// 弹幕设置面板
class DanmakuSettingsPanel extends StatelessWidget {
  final VoidCallback? onClose;
  final DanmakuService danmakuService;

  const DanmakuSettingsPanel({
    super.key,
    this.onClose,
    required this.danmakuService,
  });

  @override
  Widget build(BuildContext context) {
    final settings = danmakuService.danmakuSettings.watch(context);

    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      children: [_buildSettings(context, settings, danmakuService)],
    );
  }

  Widget _buildSettings(
    BuildContext context,
    DanmakuSettings settings,
    DanmakuService service,
  ) {
    return Column(
      children: [
        // 透明度设置
        FSlider(
          controller: FDiscreteSliderController(
            selection: FSliderSelection(max: settings.opacity),
          ),
          label: Text('透明度：${settings.opacity.toStringAsFixed(1)}'),
          tooltipBuilder: (style, value) {
            return Text(value.toStringAsFixed(1));
          },
          marks: [
            for (double i = 0; i <= 1.0; i += 0.1)
              FSliderMark(value: i, tick: false),
          ],
          onChange: (s) {
            service.updateDanmakuSettings(
              settings.copyWith(opacity: s.offset.max),
            );
          },
        ),
        const SizedBox(height: 8),
        // 字体大小设置
        FSlider(
          controller: FDiscreteSliderController(
            selection: FSliderSelection(max: (settings.fontSize - 10) / 20),
          ),
          label: Text('字体大小：${settings.fontSize.round()}'),
          tooltipBuilder: (style, value) {
            return Text('${(value * 20 + 10).round()}');
          },
          marks: [
            for (double i = 0; i <= 1.0; i += 0.05)
              FSliderMark(value: i, tick: false),
          ],
          onChange: (s) {
            service.updateDanmakuSettings(
              settings.copyWith(
                fontSize: (s.offset.max * 20 + 10).roundToDouble(),
              ),
            );
          },
        ),
        // 字体粗细设置
        FSlider(
          controller: FDiscreteSliderController(
            selection: FSliderSelection(max: settings.danmakuFontWeight / 8),
          ),
          label: Text('字体粗细：${settings.danmakuFontWeight}'),
          tooltipBuilder: (style, value) {
            return Text('${(value * 8).round()}');
          },
          marks: [
            for (double i = 0; i <= 1.0; i += 0.125)
              FSliderMark(value: i, tick: false),
          ],
          onChange: (s) {
            service.updateDanmakuSettings(
              settings.copyWith(danmakuFontWeight: (s.offset.max * 8).round()),
            );
          },
        ),
        const SizedBox(height: 8),
        // 显示时长设置
        FSlider(
          controller: FDiscreteSliderController(
            selection: FSliderSelection(max: (settings.duration - 1) / 16),
          ),
          label: Text('显示时长：${settings.duration}'),
          tooltipBuilder: (style, value) {
            return Text('${(value * 16 + 1).round()}');
          },
          marks: [
            for (double i = 0; i <= 1.0; i += 0.0625)
              FSliderMark(value: i, tick: false),
          ],
          onChange: (s) {
            service.updateDanmakuSettings(
              settings.copyWith(duration: (s.offset.max * 16 + 1).round()),
            );
          },
        ),
        const SizedBox(height: 8),
        // 弹幕显示区域
        FSlider(
          controller: FDiscreteSliderController(
            selection: FSliderSelection(max: settings.danmakuArea),
          ),
          label: Text('弹幕区域：${(settings.danmakuArea * 100).round()}%'),
          tooltipBuilder: (style, value) {
            return Text('${(value * 100).round()}%');
          },
          marks: [
            for (double i = 0; i <= 1.0; i += 0.25)
              FSliderMark(value: i, tick: false),
          ],
          onChange: (s) {
            service.updateDanmakuSettings(
              settings.copyWith(danmakuArea: s.offset.max),
            );
          },
        ),
        const SizedBox(height: 8),
        // 弹幕类型开关
        _buildSwitchSetting(
          '隐藏滚动弹幕',
          settings.hideScroll,
          (value) => service.updateDanmakuSettings(
            settings.copyWith(hideScroll: value),
          ),
        ),
        const SizedBox(height: 8),
        _buildSwitchSetting(
          '隐藏顶部弹幕',
          settings.hideTop,
          (value) =>
              service.updateDanmakuSettings(settings.copyWith(hideTop: value)),
        ),
        const SizedBox(height: 8),
        _buildSwitchSetting(
          '隐藏底部弹幕',
          settings.hideBottom,
          (value) => service.updateDanmakuSettings(
            settings.copyWith(hideBottom: value),
          ),
        ),
        const SizedBox(height: 8),
        _buildSwitchSetting(
          '显示边框',
          settings.border,
          (value) =>
              service.updateDanmakuSettings(settings.copyWith(border: value)),
        ),
      ],
    );
  }

  Widget _buildSwitchSetting(
    String title,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Text(title),
        const Spacer(),
        FSwitch(value: value, onChange: onChanged),
      ],
    );
  }
}
