import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        scrolledUnderElevation: 0,
        title: Text(
          '设置',
          style: context.theme.typography.xl2.copyWith(height: 1.2),
        ),
      ),
      body: const Center(child: Text('设置')),
    );
  }
}
