import 'package:dandanplay_flutter/page/edit_media_library.dart';
import 'package:dandanplay_flutter/page/file_explorer.dart';
import 'package:dandanplay_flutter/page/history.dart';
import 'package:dandanplay_flutter/page/player/player.dart';
import 'package:dandanplay_flutter/page/root.dart';
import 'package:dandanplay_flutter/page/setting.dart';
import 'package:dandanplay_flutter/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';

const String rootPath = '/';
const String editMediaLibraryPath = '/edit-media-library';
const String fileExplorerPath = '/file-explorer';
const String historyPath = '/history';
const String videoPlayerPath = '/video-player';
const String settingPath = '/setting';

// GoRouter configuration
final router = GoRouter(
  initialLocation: rootPath,
  routes: [
    GoRoute(path: rootPath, builder: (context, state) => const RootPage()),
    GoRoute(
      path: settingPath,
      builder: (context, state) => const SettingPage(),
    ),
    GoRoute(
      path: editMediaLibraryPath,
      builder:
          (context, state) => EditMediaLibraryPage(
            id: int.tryParse(state.uri.queryParameters['id'] ?? ''),
          ),
    ),
    GoRoute(
      path: fileExplorerPath,
      builder: (context, state) {
        return FileExplorerPage(
          mediaLibraryId:
              int.tryParse(state.uri.queryParameters['id'] ?? '0') ?? 0,
          mediaLibraryName: state.uri.queryParameters['name'] ?? '',
        );
      },
    ),
    GoRoute(
      path: historyPath,
      builder: (context, state) => const HistoryPage(),
    ),
    GoRoute(
      path: videoPlayerPath,
      pageBuilder: (context, state) {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
        return CustomTransitionPage(
          child: Theme(
            data: zincDark.toApproximateMaterialTheme(),
            child: FTheme(data: zincDark, child: VideoPlayerPage()),
          ),
          transitionsBuilder: (_, animation, _, child) {
            // 完全禁用转场动画
            return child;
          },
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
        );
      },
    ),
  ],
);
