import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dandanplay_flutter/service/player/danmaku.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/utils/format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:fvp/fvp.dart';
import 'package:get_it/get_it.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:signals_flutter/signals_flutter.dart';
import 'package:video_player/video_player.dart';
import 'player_state.dart';
import 'timer_manager.dart';
import '../history.dart';

/// 节流信号更新器
/// 用于控制信号更新频率，减少不必要的UI重建
class ThrottledSignalUpdater {
  Timer? _throttleTimer;
  final Duration _interval;

  ThrottledSignalUpdater({Duration? interval})
    : _interval = interval ?? const Duration(milliseconds: 66); // 默认15fps

  /// 节流更新方法
  /// 如果在节流间隔内，则跳过更新
  void updateWithThrottle(VoidCallback updateFn) {
    if (_throttleTimer?.isActive != true) {
      updateFn();
      _throttleTimer = Timer(_interval, () {});
    }
  }

  /// 立即更新（绕过节流）
  /// 用于关键状态变化
  void updateImmediately(VoidCallback updateFn) {
    updateFn();
  }

  /// 释放资源
  void dispose() {
    _throttleTimer?.cancel();
    _throttleTimer = null;
  }
}

/// 进度条状态复合类
/// 将位置、时长和缓冲位置合并为单一状态，减少UI重建
class ProgressBarState {
  final Duration position;
  final Duration duration;
  final Duration bufferedPosition;

  const ProgressBarState({
    required this.position,
    required this.duration,
    required this.bufferedPosition,
  });

  /// 播放进度百分比 (0.0 - 1.0)
  double get progress {
    if (duration.inMilliseconds <= 0) return 0.0;
    return (position.inMilliseconds / duration.inMilliseconds).clamp(0.0, 1.0);
  }

  /// 缓冲进度百分比 (0.0 - 1.0)
  double get bufferedProgress {
    if (duration.inMilliseconds <= 0) return 0.0;
    return (bufferedPosition.inMilliseconds / duration.inMilliseconds).clamp(
      0.0,
      1.0,
    );
  }

  /// 是否有有效的时长信息
  bool get hasValidDuration => duration.inMilliseconds > 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressBarState &&
          runtimeType == other.runtimeType &&
          position == other.position &&
          duration == other.duration &&
          bufferedPosition == other.bufferedPosition;

  @override
  int get hashCode =>
      position.hashCode ^ duration.hashCode ^ bufferedPosition.hashCode;

  @override
  String toString() =>
      'ProgressBarState(position: $position, duration: $duration, buffered: $bufferedPosition)';
}

/// 视频播放服务
/// 封装VideoPlayerController，提供状态管理和播放控制
class VideoPlayerService {
  VideoPlayerController? _controller;
  final HistoryService _historyService = GetIt.I<HistoryService>();
  final GlobalPlayerService _globalPlayerService =
      GetIt.I<GlobalPlayerService>();

  bool _isDisposed = false;

  // 播放器状态信号
  final Signal<PlayerState> _playerState = Signal(PlayerState.idle);
  final Signal<Duration> _position = Signal(Duration.zero);
  final Signal<Duration> _duration = Signal(Duration.zero);
  final Signal<Duration> _targetPosition = Signal(Duration.zero);
  final Signal<Duration> _bufferedPosition = Signal(Duration.zero);
  final Signal<double> _playbackSpeed = Signal(1.0);
  final Signal<String?> _errorMessage = Signal(null);
  final Signal<int> _bitRate = Signal(0);
  final Signal<String> _videoName = Signal('');
  late String _currentVideoPath;
  late HistoriesType _historiesType;
  late int? _mediaLibraryId;
  late Map<String, String> headers;

  // 复合信号 - 进度条状态
  late final Computed<ProgressBarState> _progressBarState;

  // 当前视频信息
  final DanmakuService danmakuService = DanmakuService();
  late History _history;

  // 节流更新器
  late final ThrottledSignalUpdater _signalThrottler;

  // 定时器管理器
  late final PlayerTimerManager _timerManager;

  VideoPlayerService() {
    // 初始化节流器，设置为15fps（66ms间隔）
    _signalThrottler = ThrottledSignalUpdater();

    // 初始化定时器管理器
    _timerManager = PlayerTimerManager();

    // 初始化复合信号 - 进度条状态
    _progressBarState = computed(
      () => ProgressBarState(
        position: _position.value,
        duration: _duration.value,
        bufferedPosition: _bufferedPosition.value,
      ),
    );
    _currentVideoPath = _globalPlayerService.currentVideoPath;
    _historiesType = _globalPlayerService.historiesType;
    _mediaLibraryId = _globalPlayerService.mediaLibraryId;
    headers = _globalPlayerService.headers;
    _videoName.value = _currentVideoPath.split('/').last;
  }

  /// 播放器状态
  ReadonlySignal<PlayerState> get playerState => _playerState.readonly();

  /// 当前播放位置
  ReadonlySignal<Duration> get position => _position.readonly();

  /// 视频总长度
  ReadonlySignal<Duration> get duration => _duration.readonly();

  /// 目标播放位置（跳转目标）
  ReadonlySignal<Duration> get targetPosition => _targetPosition.readonly();

  /// 已缓冲位置
  ReadonlySignal<Duration> get bufferedPosition => _bufferedPosition.readonly();

  /// 播放速度
  ReadonlySignal<double> get playbackSpeed => _playbackSpeed.readonly();

  /// 错误信息
  ReadonlySignal<String?> get errorMessage => _errorMessage.readonly();
  ReadonlySignal<int> get bitRate => _bitRate.readonly();
  ReadonlySignal<String> get videoName => _videoName.readonly();

  /// 进度条状态（复合信号）
  ReadonlySignal<ProgressBarState> get progressBarState =>
      _progressBarState.readonly();

  /// 视频播放器控制器
  VideoPlayerController? get controller => _controller;

  /// 是否已初始化
  bool get isInitialized => _controller?.value.isInitialized ?? false;

  /// 初始化视频播放器
  Future<void> initialize() async {
    try {
      _playerState.value = PlayerState.loading;
      _errorMessage.value = null;

      // 创建新的控制器
      if (_currentVideoPath.startsWith('http://') ||
          _currentVideoPath.startsWith('https://')) {
        // 网络视频
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(_currentVideoPath),
          httpHeaders: headers,
        );
      } else {
        // 本地文件
        _controller = VideoPlayerController.file(File(_currentVideoPath));
      }

      // 添加监听器
      _controller!.addListener(_onVideoPlayerUpdate);

      // 初始化控制器
      await _controller!.initialize();

      // 添加历史记录
      await _historyService.addHistory(
        url: _globalPlayerService.virtualVideoPath,
        headers: jsonEncode(headers),
        duration: _duration.value,
        type: _historiesType,
        mediaLibraryId: _mediaLibraryId,
      );
      _history =
          await _historyService.getPlaybackHistory(
            _globalPlayerService.virtualVideoPath,
          ) ??
          History(
            id: 0,
            uniqueKey: '',
            duration: 0,
            position: 0,
            url: '',
            headers: '',
            updateTime: 0,
            type: HistoriesType.local,
          );

      danmakuService.virtualVideoPath = _globalPlayerService.virtualVideoPath;
      danmakuService.history = _history;
      danmakuService.duration = _duration.value;

      // 恢复播放进度
      await _restoreProgress();
      // 更新状态
      //_playerState.value = PlayerState.ready;
      _duration.value = _controller!.value.duration;

      // 自动播放
      await play();

      // 加载弹幕
      danmakuService.loadDanmaku();

      // 开始定时更新播放历史
      _startPositionTimer();
    } catch (e) {
      _playerState.value = PlayerState.error;
      _errorMessage.value = e.toString();
      rethrow;
    }
  }

  /// 播放视频
  Future<void> play() async {
    if (_controller != null && _playerState.value.canPlay) {
      await _controller!.play();
      _playerState.value = PlayerState.playing;
      // 更新定时器管理器播放状态
      _timerManager.updatePlayingState(true);
    }
  }

  /// 暂停视频
  Future<void> pause() async {
    if (_controller != null && _playerState.value.canPause) {
      await _controller!.pause();
      _playerState.value = PlayerState.paused;
      // 更新定时器管理器播放状态
      _timerManager.updatePlayingState(false);
    }
  }

  /// 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    if (_controller != null && _controller!.value.isInitialized) {
      // 清空弹幕并重置位置
      danmakuService.clear();
      danmakuService.resetDanmakuPosition();

      _targetPosition.value = position;
      await _controller!.seekTo(position);
    }
  }

  /// 相对跳转
  void seekRelative(Duration offset) {
    final currentPosition = position.value;
    final newPosition = currentPosition + offset;
    seekTo(newPosition); // seekTo方法已经包含了弹幕清空逻辑

    // 显示跳转通知
    final offsetText =
        offset.isNegative
            ? '-${formatDuration(-offset)}'
            : '+${formatDuration(offset)}';
    _globalPlayerService.showNotification('跳转 $offsetText');
  }

  /// 设置播放速度
  Future<void> setPlaybackSpeed(double speed) async {
    if (_controller != null && _controller!.value.isInitialized) {
      await _controller!.setPlaybackSpeed(speed);
      _playbackSpeed.value = speed;
    }
  }

  Future<void> doubleSpeed(bool isDouble) async {
    final currentSpeed = _playbackSpeed.value;
    final newSpeed = currentSpeed * (isDouble ? 2 : 0.5);
    await setPlaybackSpeed(newSpeed);
  }

  /// 切换播放/暂停
  Future<void> togglePlayPause() async {
    if (_playerState.value.isPlaying) {
      await pause();
      danmakuService.syncWithVideo(false);
    } else if (_playerState.value.canPlay) {
      await play();
      danmakuService.syncWithVideo(true);
    }
  }

  /// 视频播放器状态更新回调
  /// 使用节流机制优化更新频率，关键状态变化立即更新
  void _onVideoPlayerUpdate() {
    if (_isDisposed || _controller == null) return;

    final value = _controller!.value;
    final currentState = _playerState.value;

    // 检查是否有关键状态变化（需要立即更新）
    final hasError = value.hasError;
    final isBuffering = value.isBuffering;
    final isPlaying = value.isPlaying;
    final isInitialized = value.isInitialized;

    PlayerState newState = currentState;
    if (hasError) {
      newState = PlayerState.error;
    } else if (isBuffering) {
      newState = PlayerState.buffering;
    } else if (isPlaying) {
      newState = PlayerState.playing;
    } else if (isInitialized) {
      newState = PlayerState.paused;
    }

    // 关键状态变化立即更新
    if (newState != currentState || hasError) {
      _signalThrottler.updateImmediately(() {
        _playerState.value = newState;
        if (hasError) {
          _errorMessage.value = value.errorDescription;
        }
      });
    }

    // 位置和缓冲信息使用节流更新
    _signalThrottler.updateWithThrottle(() {
      // 更新播放位置
      _position.value = value.position;

      _bitRate.value = _controller!.getMediaInfo()?.bitRate ?? 0;

      // 更新缓冲位置
      if (value.buffered.isNotEmpty) {
        _bufferedPosition.value = value.buffered.last.end;
      }

      // 更新弹幕显示位置
      danmakuService.updatePlayPosition(value.position);
    });
  }

  /// 更新播放历史记录
  Future<void> updatePlaybackHistory() async {
    await _historyService.updateProgress(
      id: _history.id,
      position: _position.value,
      duration: _duration.value,
    );
  }

  Future<void> saveSnapshot() async {
    try {
      if (_history.uniqueKey.isEmpty) {
        debugPrint('VideoPlayerService: Cannot get video unique key');
        return;
      }

      if (_controller == null || !_controller!.value.isInitialized) {
        debugPrint('VideoPlayerService: Player not ready for snapshot');
        return;
      }

      final videoAspectRatio = _controller!.value.aspectRatio;
      if (videoAspectRatio <= 0) {
        debugPrint('VideoPlayerService: Invalid video aspect ratio');
        return;
      }
      final height = (300 / _controller!.value.aspectRatio).round();
      var srcImageBytes = await _controller!.snapshot(
        height: height,
        width: 300,
      );
      if (srcImageBytes == null) {
        return;
      }

      final image = img.Image.fromBytes(
        width: 300,
        height: height,
        bytes: srcImageBytes.buffer,
        order: img.ChannelOrder.rgba,
      );

      final imageBytes = img.encodeJpg(image);

      final documentsDir = await getApplicationDocumentsDirectory();

      // 缩略图保存到应用内部目录
      final dir = Directory('${documentsDir.path}/screenshots');
      await dir.create(recursive: true);
      final file = File('${dir.path}/${_history.uniqueKey}');
      await file.writeAsBytes(imageBytes);
    } catch (e) {
      debugPrint('VideoPlayerService: 快照保存异常: $e');
      return;
    }
  }

  /// 启动定时器任务
  /// 使用PlayerTimerManager统一管理定时器任务
  void _startPositionTimer() {
    _timerManager.scheduleTask(() async {
      await updatePlaybackHistory();
    });
    _timerManager.scheduleTask(() async {
      await saveSnapshot();
    });

    // 设置初始播放状态
    _timerManager.updatePlayingState(_playerState.value.isPlaying);
  }

  /// 恢复播放进度
  Future<Duration> restoreProgress() async {
    if (_history.position > 0) {
      final position = Duration(milliseconds: _history.position);
      await seekTo(position);
      return position;
    }
    return Duration.zero;
  }

  Future<void> _restoreProgress() async {
    try {
      if (_history.position > 0) {
        // 显示恢复播放通知
        final position = Duration(milliseconds: _history.position);
        await seekTo(position);
        final positionText = formatDuration(position);
        _globalPlayerService.showNotification('已恢复到 $positionText');

        debugPrint('恢复播放历史: $positionText');
      }
    } catch (e) {
      debugPrint('恢复播放历史失败: $e');
    }
  }

  /// 释放控制器
  Future<void> _disposeController() async {
    // 停止定时器管理器的所有任务
    _timerManager.dispose();
    // 释放节流器
    _signalThrottler.dispose();

    if (_controller != null) {
      _controller!.removeListener(_onVideoPlayerUpdate);
      await _controller!.dispose();
      _controller = null;
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    if (_isDisposed) {
      return;
    }
    _isDisposed = true;

    // 在释放资源前，确保所有数据都已保存
    try {
      await updatePlaybackHistory();
    } catch (e) {
      debugPrint('VideoPlayerService: 保存最终历史记录失败: $e');
    }

    await _disposeController();
    // 释放复合信号
    _progressBarState.dispose();

    // 释放信号
    _playerState.dispose();
    _position.dispose();
    _duration.dispose();
    _targetPosition.dispose();
    _bufferedPosition.dispose();
    _playbackSpeed.dispose();
    _bitRate.dispose();
    _videoName.dispose();
    _errorMessage.dispose();
    danmakuService.dispose();
  }
}
