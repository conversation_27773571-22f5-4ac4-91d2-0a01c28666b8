import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 网络视频hash计算结果
class NetworkVideoHashResult {
  final String hash;
  final int downloadedBytes;
  final int? totalFileSize;
  final bool isRangeSupported;
  final bool isPartialHash;

  const NetworkVideoHashResult({
    required this.hash,
    required this.downloadedBytes,
    this.totalFileSize,
    required this.isRangeSupported,
    required this.isPartialHash,
  });
}

/// 弹幕优化器
/// 提供异步hash计算、缓存优化等功能
class DanmakuOptimizer {
  /// 异步计算本地视频文件hash
  /// 使用Isolate处理，避免阻塞主线程
  static Future<String> calculateVideoHashAsync(String filePath) async {
    try {
      // 使用compute在Isolate中计算hash
      return await compute(_calculateVideoHashInIsolate, filePath);
    } catch (e) {
      debugPrint('异步计算视频hash失败: $e');
      rethrow;
    }
  }

  /// 异步计算网络视频hash
  /// 使用Isolate处理下载和hash计算
  static Future<NetworkVideoHashResult> calculateNetworkVideoHashAsync(
    String url, {
    Map<String, String>? headers,
    Function(int downloaded, int? total)? onProgress,
  }) async {
    try {
      final params = {'url': url, 'headers': headers ?? <String, String>{}};

      // 使用compute在Isolate中处理网络下载和hash计算
      final result = await compute(_calculateNetworkHashInIsolate, params);

      // 处理进度回调（在主线程中）
      if (onProgress != null) {
        onProgress(
          result['downloadedBytes'] as int,
          result['totalFileSize'] as int?,
        );
      }

      return NetworkVideoHashResult(
        hash: result['hash'] as String,
        downloadedBytes: result['downloadedBytes'] as int,
        totalFileSize: result['totalFileSize'] as int?,
        isRangeSupported: result['isRangeSupported'] as bool,
        isPartialHash: result['isPartialHash'] as bool,
      );
    } catch (e) {
      debugPrint('异步计算网络视频hash失败: $e');
      rethrow;
    }
  }

  /// 在Isolate中计算本地视频文件hash
  static Future<String> _calculateVideoHashInIsolate(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('文件不存在', filePath);
      }

      // 读取前16MB数据
      const int maxBytes = 16 * 1024 * 1024; // 16MB
      final fileSize = await file.length();
      final bytesToRead = fileSize < maxBytes ? fileSize : maxBytes;

      final randomAccessFile = await file.open();
      final bytes = await randomAccessFile.read(bytesToRead);
      await randomAccessFile.close();

      // 计算MD5
      final digest = md5.convert(bytes);
      return digest.toString();
    } catch (e) {
      throw Exception('计算视频哈希值失败: $e');
    }
  }

  /// 在Isolate中计算网络视频hash
  static Future<Map<String, dynamic>> _calculateNetworkHashInIsolate(
    Map<String, dynamic> params,
  ) async {
    final url = params['url'] as String;
    final headers = params['headers'] as Map<String, String>;

    final dio = Dio();
    const int maxBytes = 16 * 1024 * 1024; // 16MB

    try {
      // 首先尝试HEAD请求获取文件大小
      int? totalSize;
      try {
        final headResponse = await dio.head(
          url,
          options: Options(headers: headers),
        );
        final contentLength = headResponse.headers.value('content-length');
        if (contentLength != null) {
          totalSize = int.tryParse(contentLength);
        }
      } catch (e) {
        // HEAD请求失败，继续使用GET请求
      }

      // 使用Range请求下载前16MB
      final rangeHeaders = {...headers, 'Range': 'bytes=0-${maxBytes - 1}'};

      final response = await dio.get<Uint8List>(
        url,
        options: Options(
          headers: rangeHeaders,
          responseType: ResponseType.bytes,
        ),
      );

      if (response.data == null) {
        throw Exception('下载数据为空');
      }

      final bytes = response.data!;
      final actualSize = bytes.length;

      // 检查是否支持Range请求
      final isRangeSupported = response.statusCode == 206;

      if (!isRangeSupported && actualSize > maxBytes) {
        // 服务器不支持Range请求，但文件太大，只取前16MB
        final truncatedBytes = bytes.take(maxBytes).toList();
        final digest = md5.convert(truncatedBytes);

        return {
          'hash': digest.toString(),
          'downloadedBytes': maxBytes,
          'totalFileSize': totalSize,
          'isRangeSupported': false,
          'isPartialHash': true,
        };
      } else {
        // 计算实际下载数据的hash
        final digest = md5.convert(bytes);

        return {
          'hash': digest.toString(),
          'downloadedBytes': actualSize,
          'totalFileSize': totalSize ?? actualSize,
          'isRangeSupported': isRangeSupported,
          'isPartialHash': actualSize < (totalSize ?? actualSize),
        };
      }
    } catch (e) {
      throw Exception('网络视频hash计算失败: $e');
    } finally {
      dio.close();
    }
  }

  /// 智能缓存策略
  /// 根据视频类型和使用频率决定缓存策略
  static bool shouldCacheDanmaku({
    required String videoPath,
    required int danmakuCount,
    required bool isNetworkVideo,
  }) {
    // 网络视频总是缓存
    if (isNetworkVideo) return true;

    // 弹幕数量太少不缓存
    if (danmakuCount < 10) return false;

    // 本地视频根据文件大小决定
    try {
      final file = File(videoPath);
      final fileSize = file.lengthSync();
      // 大于100MB的视频文件缓存弹幕
      return fileSize > 100 * 1024 * 1024;
    } catch (e) {
      return true; // 出错时默认缓存
    }
  }

  /// 获取缓存过期时间
  /// 根据弹幕数量和视频类型返回不同的过期时间
  static Duration getCacheExpiration({
    required int danmakuCount,
    required bool isNetworkVideo,
  }) {
    if (isNetworkVideo) {
      // 网络视频缓存时间较短
      return danmakuCount > 1000
          ? const Duration(days: 7) // 热门视频缓存7天
          : const Duration(days: 3); // 普通视频缓存3天
    } else {
      // 本地视频缓存时间较长
      return danmakuCount > 1000
          ? const Duration(days: 30) // 热门视频缓存30天
          : const Duration(days: 14); // 普通视频缓存14天
    }
  }

  /// 预加载相关弹幕
  /// 根据当前播放的视频预测可能播放的下一集
  static List<String> predictNextEpisodes(String virtualVideoPath) {
    final predictions = <String>[];

    try {
      final file = File(virtualVideoPath);
      final directory = file.parent;
      final fileName = file.uri.pathSegments.last;

      // 简单的集数预测逻辑
      final episodePattern = RegExp(r'(\d+)');
      final matches = episodePattern.allMatches(fileName);

      if (matches.isNotEmpty) {
        final lastMatch = matches.last;
        final episodeNumber = int.tryParse(lastMatch.group(1)!);

        if (episodeNumber != null) {
          // 预测下一集和下下集
          for (int i = 1; i <= 2; i++) {
            final nextEpisode = episodeNumber + i;
            final nextFileName = fileName.replaceAll(
              lastMatch.group(1)!,
              nextEpisode.toString().padLeft(lastMatch.group(1)!.length, '0'),
            );
            final nextPath = '${directory.path}/$nextFileName';

            if (File(nextPath).existsSync()) {
              predictions.add(nextPath);
            }
          }
        }
      }
    } catch (e) {
      debugPrint('预测下一集失败: $e');
    }

    return predictions;
  }
}
