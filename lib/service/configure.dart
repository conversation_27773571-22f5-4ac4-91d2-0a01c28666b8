import 'dart:convert';
import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/config_item.dart';

/// 配置服务
/// 提供类型安全的配置项管理，支持默认值和持久化存储
class ConfigureService {
  late SharedPreferences _prefs;

  ConfigureService();

  /// 注册配置服务到依赖注入容器
  static Future<void> register() async {
    var service = ConfigureService();
    await service.init();
    GetIt.I.registerSingleton<ConfigureService>(service);
  }

  /// 初始化配置服务
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      debugPrint('ConfigureService: 初始化完成');
    } catch (e) {
      debugPrint('ConfigureService: 初始化失败 - $e');
      rethrow;
    }
  }

  /// 通用获取配置值方法
  /// 支持类型安全的配置获取，如果配置不存在则返回默认值
  Future<T> get<T>(ConfigKey key) async {
    try {
      final item = ConfigRegistry.getItem<T>(key);
      if (item == null) {
        throw ArgumentError('未找到配置项: $key');
      }

      final storageKey = item.storageKey;

      // 根据类型获取对应的值
      if (T == bool) {
        return (_prefs.getBool(storageKey) ?? item.defaultValue) as T;
      } else if (T == int) {
        return (_prefs.getInt(storageKey) ?? item.defaultValue) as T;
      } else if (T == double) {
        return (_prefs.getDouble(storageKey) ?? item.defaultValue) as T;
      } else if (T == String) {
        return (_prefs.getString(storageKey) ?? item.defaultValue) as T;
      } else {
        // 对于复杂类型，尝试JSON反序列化
        final jsonString = _prefs.getString(storageKey);
        if (jsonString != null) {
          try {
            return jsonDecode(jsonString) as T;
          } catch (e) {
            debugPrint('ConfigureService: JSON反序列化失败 - $e');
          }
        }
        return item.defaultValue;
      }
    } catch (e) {
      debugPrint('ConfigureService: 获取配置失败 [$key] - $e');
      // 返回默认值作为fallback
      final defaultValue = ConfigRegistry.getDefaultValue<T>(key);
      return defaultValue ?? (null as T);
    }
  }

  /// 通用设置配置值方法
  /// 支持类型安全的配置设置和持久化存储
  Future<void> set<T>(ConfigKey key, T value) async {
    try {
      final item = ConfigRegistry.getItem<T>(key);
      if (item == null) {
        throw ArgumentError('未找到配置项: $key');
      }

      final storageKey = item.storageKey;

      // 根据类型设置对应的值
      if (T == bool) {
        await _prefs.setBool(storageKey, value as bool);
      } else if (T == int) {
        await _prefs.setInt(storageKey, value as int);
      } else if (T == double) {
        await _prefs.setDouble(storageKey, value as double);
      } else if (T == String) {
        await _prefs.setString(storageKey, value as String);
      } else {
        // 对于复杂类型，使用JSON序列化
        final jsonString = jsonEncode(value);
        await _prefs.setString(storageKey, jsonString);
      }

      debugPrint('ConfigureService: 设置配置成功 [$key] = $value');
    } catch (e) {
      debugPrint('ConfigureService: 设置配置失败 [$key] - $e');
      rethrow;
    }
  }

  /// 重置配置项到默认值
  Future<void> resetToDefault(ConfigKey key) async {
    try {
      final storageKey = ConfigRegistry.getStorageKey(key);
      if (storageKey != null) {
        await _prefs.remove(storageKey);
        debugPrint('ConfigureService: 重置配置到默认值 [$key]');
      }
    } catch (e) {
      debugPrint('ConfigureService: 重置配置失败 [$key] - $e');
      rethrow;
    }
  }

  /// 重置所有配置项到默认值
  Future<void> resetAllToDefault() async {
    try {
      final allItems = ConfigRegistry.allItems;
      for (final item in allItems.values) {
        await _prefs.remove(item.storageKey);
      }
      debugPrint('ConfigureService: 重置所有配置到默认值');
    } catch (e) {
      debugPrint('ConfigureService: 重置所有配置失败 - $e');
      rethrow;
    }
  }

  /// 获取所有配置项的当前值
  Future<Map<ConfigKey, dynamic>> getAll() async {
    final result = <ConfigKey, dynamic>{};
    final allItems = ConfigRegistry.allItems;

    for (final entry in allItems.entries) {
      try {
        final key = entry.key;
        final item = entry.value;

        // 根据默认值类型推断实际类型
        final defaultValue = item.defaultValue;
        if (defaultValue is bool) {
          result[key] = await get<bool>(key);
        } else if (defaultValue is int) {
          result[key] = await get<int>(key);
        } else if (defaultValue is double) {
          result[key] = await get<double>(key);
        } else if (defaultValue is String) {
          result[key] = await get<String>(key);
        } else {
          result[key] = await get<dynamic>(key);
        }
      } catch (e) {
        debugPrint('ConfigureService: 获取配置失败 [${entry.key}] - $e');
      }
    }

    return result;
  }

  /// 批量设置配置项
  Future<void> setAll(Map<ConfigKey, dynamic> configs) async {
    for (final entry in configs.entries) {
      try {
        final key = entry.key;
        final value = entry.value;

        // 根据值类型调用对应的set方法
        if (value is bool) {
          await set<bool>(key, value);
        } else if (value is int) {
          await set<int>(key, value);
        } else if (value is double) {
          await set<double>(key, value);
        } else if (value is String) {
          await set<String>(key, value);
        } else {
          await set<dynamic>(key, value);
        }
      } catch (e) {
        debugPrint('ConfigureService: 批量设置配置失败 [${entry.key}] - $e');
      }
    }
  }

  Future<DanmakuSettings> getDanmakuSettings() async {
    final jsonString = await get<String>(ConfigKey.danmakuSettings);
    return DanmakuSettings.fromJson(
      jsonDecode(utf8.decode(base64Decode(jsonString))),
    );
  }

  Future<void> setDanmakuSettings(DanmakuSettings settings) async {
    await set<String>(
      ConfigKey.danmakuSettings,
      base64Encode(utf8.encode(jsonEncode(settings.toJson()))),
    );
  }

  // ==================== 界面相关配置的类型安全方法 ====================

  /// 获取主题模式
  Future<int> getThemeMode() async {
    return await get<int>(ConfigKey.themeMode);
  }

  /// 设置主题模式
  /// [mode] 0: 跟随系统, 1: 浅色主题, 2: 深色主题
  Future<void> setThemeMode(int mode) async {
    // 确保主题模式在有效范围内
    final clampedMode = mode.clamp(0, 2);
    await set<int>(ConfigKey.themeMode, clampedMode);
  }

  /// 获取语言设置
  Future<String> getLanguage() async {
    return await get<String>(ConfigKey.language);
  }

  /// 设置语言
  Future<void> setLanguage(String language) async {
    await set<String>(ConfigKey.language, language);
  }

  // ==================== 便捷方法 ====================

  /// 检查配置项是否存在（非默认值）
  Future<bool> hasValue(ConfigKey key) async {
    final storageKey = ConfigRegistry.getStorageKey(key);
    if (storageKey == null) return false;
    return _prefs.containsKey(storageKey);
  }

  /// 获取所有已设置的配置项键
  Future<Set<ConfigKey>> getSetKeys() async {
    final setKeys = <ConfigKey>{};
    final allItems = ConfigRegistry.allItems;

    for (final entry in allItems.entries) {
      if (await hasValue(entry.key)) {
        setKeys.add(entry.key);
      }
    }

    return setKeys;
  }

  /// 导出配置为JSON字符串
  Future<String> exportToJson() async {
    final allConfigs = await getAll();
    final exportData = <String, dynamic>{};

    for (final entry in allConfigs.entries) {
      exportData[entry.key.name] = entry.value;
    }

    return jsonEncode(exportData);
  }

  /// 从JSON字符串导入配置
  Future<void> importFromJson(String jsonString) async {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final configs = <ConfigKey, dynamic>{};

      for (final entry in data.entries) {
        // 尝试将字符串键转换为ConfigKey枚举
        try {
          final key = ConfigKey.values.firstWhere((k) => k.name == entry.key);
          configs[key] = entry.value;
        } catch (e) {
          debugPrint('ConfigureService: 跳过未知配置项 ${entry.key}');
        }
      }

      await setAll(configs);
      debugPrint('ConfigureService: 从JSON导入配置成功');
    } catch (e) {
      debugPrint('ConfigureService: 从JSON导入配置失败 - $e');
      rethrow;
    }
  }
}
