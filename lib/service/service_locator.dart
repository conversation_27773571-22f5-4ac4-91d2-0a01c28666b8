import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/player/global.dart';
import 'package:get_it/get_it.dart';
import 'storage.dart';
import 'history.dart';

/// 服务定位器配置
/// 管理所有服务的依赖注入
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;

  /// 初始化所有服务
  static Future<void> initialize() async {
    await StorageService.register();
    await MediaLibraryService.register();
    await ConfigureService.register();
    await GlobalPlayerService.register();
    FileExplorerService.register();
    await HistoryService.register();
  }

  /// 获取服务实例
  static T get<T extends Object>() => _getIt.get<T>();

  /// 重置所有服务（用于测试）
  static Future<void> reset() async {
    await _getIt.reset();
  }
}
