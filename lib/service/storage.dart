import 'package:drift/drift.dart';
import 'package:drift_flutter/drift_flutter.dart';
import 'package:get_it/get_it.dart';

part 'storage.g.dart';

@DriftDatabase(tables: [MediaLibraries, Histories])
class StorageService extends _$StorageService {
  // 测试用构造函数
  StorageService() : super(driftDatabase(name: 'db'));

  @override
  int get schemaVersion => 1;

  static Future<void> register() async {
    final db = StorageService();
    GetIt.I.registerSingleton<StorageService>(db);
  }

  Future<List<MediaLibrary>> getMediaLibraries() =>
      select(mediaLibraries).get();
  Future<MediaLibrary?> getMediaLibrary(int id) =>
      (select(mediaLibraries)
        ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  Future<int> createMediaLibrary(MediaLibrariesCompanion mediaLibrary) =>
      into(mediaLibraries).insert(mediaLibrary);
  Future<void> updateMediaLibrary(MediaLibrariesCompanion mediaLibrary) =>
      into(mediaLibraries).insertOnConflictUpdate(mediaLibrary);
  Future<void> deleteMediaLibrary(int id) =>
      (delete(mediaLibraries)..where((tbl) => tbl.id.equals(id))).go();

  Future<List<History>> getHistories() =>
      (select(histories)
        ..orderBy([(t) => OrderingTerm.desc(t.updateTime)])).get();

  Future<List<History>> getHistoriesWithPagination(int limit, int offset) =>
      (select(histories)
            ..orderBy([(t) => OrderingTerm.desc(t.updateTime)])
            ..limit(limit, offset: offset))
          .get();
  Future<History?> getHistory(int id) =>
      (select(histories)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  Future<History?> getHistoryByUniqueKey(String uniqueKey) =>
      (select(histories)
        ..where((tbl) => tbl.uniqueKey.equals(uniqueKey))).getSingleOrNull();
  Future<void> createHistory(HistoriesCompanion history) =>
      into(histories).insert(history);
  Future<void> updateHistory(HistoriesCompanion history) =>
      (update(histories)
        ..where((tbl) => tbl.id.equals(history.id.value))).write(history);
  Future<void> updateProgress(HistoriesCompanion history) =>
      (update(histories)
        ..where((tbl) => tbl.id.equals(history.id.value))).write(history);
  Future<void> deleteHistory(int id) =>
      (delete(histories)..where((tbl) => tbl.id.equals(id))).go();
  Future<void> clearAllHistories() => delete(histories).go();
}

enum MediaLibraryType { webdav, ftp, smb, local }

enum HistoriesType { netWork, local, mediaLibrary }

class Histories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get uniqueKey => text().unique()();
  IntColumn get duration => integer()();
  IntColumn get position => integer()();
  TextColumn get url => text().nullable()();
  TextColumn get headers => text().nullable()();
  IntColumn get type => intEnum<HistoriesType>()();
  IntColumn get mediaLibraryId => integer().nullable()();
  IntColumn get updateTime => integer()();
}

class MediaLibraries extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text()();
  TextColumn get url => text()();
  IntColumn get port => integer().nullable()();
  TextColumn get headers => text()();
  IntColumn get mediaLibraryType => intEnum<MediaLibraryType>()();
  TextColumn get account => text().nullable()();
  TextColumn get password => text().nullable()();
  BoolColumn get isAnonymous => boolean().withDefault(const Constant(false))();
}
