// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';

// import 'package:forui/forui.dart';

// // ignore_for_file: unnecessary_ignore
// // ignore_for_file: avoid_redundant_argument_values

// /// Generated by Forui CLI.
// ///
// /// Modify the generated function bodies to create your own custom style.
// /// Then, call the modified functions and pass the results to your FThemeData.
// ///
// /// ### Example
// /// Generated style:
// /// ```dart
// /// // Modify this function's body.
// /// FDividerStyles dividerStyles({
// ///   required FColorScheme color,
// ///   required FStyle style,
// /// }) => FDividerStyles(
// ///   horizontalStyle: FDividerStyle(
// ///     colors: colors.secondary,
// ///     padding: FDividerStyle.defaultPadding.horizontalStyle,
// ///     width: style.borderWidth,
// ///   ),
// ///   verticalStyle: FDividerStyle(
// ///     colors: colors.secondary,
// ///     padding: FDividerStyle.defaultPadding.verticalStyle,
// ///     width: style.borderWidth,
// ///   ),
// /// );
// /// ```
// ///
// /// File that contains your `FThemeData`:
// /// ```dart
// /// import 'package:my_application/theme/divider_style.dart' // Your generated style file.
// ///
// /// FThemeData(
// ///  color: FThemes.zinc.light.color,
// ///  style: FThemes.zinc.light.style,
// ///  dividerStyles: CustomFDividerStyles.dividerStyles( // The function in your generated style file.
// ///    color: FThemes.zinc.light.color,
// ///    style: FThemes.zinc.light.style,
// ///   ),
// /// );
// /// ```
// ///
// /// A theme can be generated using the Forui CLI:
// /// ```shell
// /// dart forui theme create [theme name]
// /// ```
// ///
// /// See https://forui.dev/docs/themes#customize-themes for more information.
// FTileGroupStyle tileGroupStyle({
//   required FColors colors,
//   required FTypography typography,
//   required FStyle style,
//   required FColors newColors,
// }) => FTileGroupStyle(
//   borderColor: newColors.border,
//   borderWidth: style.borderWidth,
//   borderRadius: BorderRadiusGeometry.all(Radius.zero),
//   tileStyle: _tileStyle(
//     colors: colors,
//     typography: typography,
//     style: style,
//     newColors: newColors,
//   ),
//   labelTextStyle: FWidgetStateMap({
//     WidgetState.error: typography.base.copyWith(
//       color:
//           style.formFieldStyle.labelTextStyle.maybeResolve({})?.color ??
//           colors.primary,
//       fontWeight: FontWeight.w600,
//     ),
//     WidgetState.disabled: typography.base.copyWith(
//       color:
//           style.formFieldStyle.labelTextStyle.maybeResolve({
//             WidgetState.disabled,
//           })?.color ??
//           colors.disable(colors.primary),
//       fontWeight: FontWeight.w600,
//     ),
//     WidgetState.any: typography.base.copyWith(
//       color:
//           style.formFieldStyle.labelTextStyle.maybeResolve({})?.color ??
//           colors.primary,
//       fontWeight: FontWeight.w600,
//     ),
//   }),
//   descriptionTextStyle: style.formFieldStyle.descriptionTextStyle.map(
//     (s) => typography.xs.copyWith(color: s.color),
//   ),
//   errorTextStyle: typography.xs.copyWith(
//     color: style.formFieldStyle.errorTextStyle.color,
//   ),
//   labelPadding: const EdgeInsets.symmetric(vertical: 7.7),
//   descriptionPadding: const EdgeInsets.only(top: 7.5),
//   errorPadding: const EdgeInsets.only(top: 5),
//   childPadding: EdgeInsets.zero,
// );

// FTileStyle _tileStyle({
//   required FColors colors,
//   required FTypography typography,
//   required FStyle style,
//   required FColors newColors,
// }) {
//   final border = FWidgetStateMap({
//     WidgetState.focused: Border.all(
//       width: style.borderWidth,
//       color: colors.primary,
//     ),
//     WidgetState.any: Border.all(
//       width: style.borderWidth,
//       color: newColors.border,
//     ),
//   });
//   final divider = FWidgetStateMap({
//     WidgetState.any: FDividerStyle(
//       color: colors.border,
//       width: style.borderWidth,
//       padding: EdgeInsets.zero,
//     ),
//   });
//   return FTileStyle(
//     pressable: FTileStateStyle(
//       border: border,
//       borderRadius: BorderRadius.all(Radius.zero),
//       backgroundColor: FWidgetStateMap({
//         WidgetState.hovered | WidgetState.pressed: colors.secondary,
//         WidgetState.any: colors.background,
//       }),
//       dividerStyle: divider,
//       contentStyle: _tileContentStyle(colors: colors, typography: typography),
//       tappableStyle: style.tappableStyle.copyWith(
//         animationTween: FTappableAnimations.none,
//         pressedEnterDuration: Duration.zero,
//         pressedExitDuration: const Duration(milliseconds: 25),
//       ),
//     ),
//     unpressable: FTileStateStyle(
//       border: border,
//       borderRadius: style.borderRadius,
//       backgroundColor: FWidgetStateMap({WidgetState.any: colors.background}),
//       dividerStyle: divider,
//       contentStyle: _tileContentStyle(colors: colors, typography: typography),
//       tappableStyle: style.tappableStyle.copyWith(
//         cursor: FWidgetStateMap.all(MouseCursor.defer),
//         animationTween: FTappableAnimations.none,
//         pressedEnterDuration: Duration.zero,
//         pressedExitDuration: const Duration(milliseconds: 25),
//       ),
//     ),
//   );
// }

// FTileContentStyle _tileContentStyle({
//   required FColors colors,
//   required FTypography typography,
// }) => FTileContentStyle(
//   prefixIconStyle: FWidgetStateMap({
//     WidgetState.any: IconThemeData(color: colors.primary, size: 40),
//   }),
//   titleTextStyle: FWidgetStateMap({WidgetState.any: typography.base}),
//   subtitleTextStyle: FWidgetStateMap({
//     WidgetState.any: typography.xs.copyWith(color: colors.mutedForeground),
//   }),
//   detailsTextStyle: FWidgetStateMap({
//     WidgetState.any: typography.base.copyWith(color: colors.mutedForeground),
//   }),
//   suffixIconStyle: FWidgetStateMap({
//     WidgetState.any: IconThemeData(color: colors.mutedForeground, size: 18),
//   }),
//   padding: const EdgeInsetsDirectional.fromSTEB(15, 13, 10, 13),
//   prefixIconSpacing: 10,
//   titleSpacing: 3,
//   middleSpacing: 4,
//   suffixIconSpacing: 5,
// );
// FTileStyle videoTileStyle({
//   required FColors colors,
//   required FTypography typography,
//   required FStyle style,
//   required FColors newColors,
// }) {
//   final border = FWidgetStateMap({
//     WidgetState.focused: Border.all(
//       width: style.borderWidth,
//       color: colors.primary,
//     ),
//     WidgetState.any: Border.all(
//       width: style.borderWidth,
//       color: newColors.border,
//     ),
//   });
//   final divider = FWidgetStateMap({
//     WidgetState.any: FDividerStyle(
//       color: colors.border,
//       width: style.borderWidth,
//       padding: EdgeInsets.zero,
//     ),
//   });
//   return FTileStyle(
//     pressable: FTileStateStyle(
//       border: border,
//       borderRadius: BorderRadius.all(Radius.zero),
//       backgroundColor: FWidgetStateMap({
//         WidgetState.hovered | WidgetState.pressed: colors.secondary,
//         WidgetState.any: colors.background,
//       }),
//       dividerStyle: divider,
//       contentStyle: _videoTileContentStyle(
//         colors: colors,
//         typography: typography,
//       ),
//       tappableStyle: style.tappableStyle.copyWith(
//         animationTween: FTappableAnimations.none,
//         pressedEnterDuration: Duration.zero,
//         pressedExitDuration: const Duration(milliseconds: 25),
//       ),
//     ),
//     unpressable: FTileStateStyle(
//       border: border,
//       borderRadius: style.borderRadius,
//       backgroundColor: FWidgetStateMap({WidgetState.any: colors.background}),
//       dividerStyle: divider,
//       contentStyle: _tileContentStyle(colors: colors, typography: typography),
//       tappableStyle: style.tappableStyle.copyWith(
//         cursor: FWidgetStateMap.all(MouseCursor.defer),
//         animationTween: FTappableAnimations.none,
//         pressedEnterDuration: Duration.zero,
//         pressedExitDuration: const Duration(milliseconds: 25),
//       ),
//     ),
//   );
// }

// FTileContentStyle _videoTileContentStyle({
//   required FColors colors,
//   required FTypography typography,
// }) => FTileContentStyle(
//   prefixIconStyle: FWidgetStateMap({
//     WidgetState.any: IconThemeData(color: colors.primary, size: 60),
//   }),
//   titleTextStyle: FWidgetStateMap({
//     WidgetState.any: typography.base.copyWith(),
//   }),
//   subtitleTextStyle: FWidgetStateMap({
//     WidgetState.any: typography.xs.copyWith(color: colors.mutedForeground),
//   }),
//   detailsTextStyle: FWidgetStateMap({
//     WidgetState.any: typography.base.copyWith(color: colors.mutedForeground),
//   }),
//   suffixIconStyle: FWidgetStateMap({
//     WidgetState.any: IconThemeData(color: colors.mutedForeground, size: 18),
//   }),
//   padding: const EdgeInsetsDirectional.fromSTEB(15, 13, 10, 13),
//   prefixIconSpacing: 10,
//   titleSpacing: 3,
//   middleSpacing: 4,
//   suffixIconSpacing: 5,
// );
